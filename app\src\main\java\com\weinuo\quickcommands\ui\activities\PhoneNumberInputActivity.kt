package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.runtime.collectAsState
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueSaveButton
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.components.IOSStyleCardInput

/**
 * 电话号码输入Activity
 */
class PhoneNumberInputActivity : ComponentActivity() {

    companion object {
        const val EXTRA_CURRENT_NUMBER = "current_number"
        const val RESULT_PHONE_NUMBER = "phone_number_result"

        fun startForResult(context: Context, currentNumber: String = "") {
            val intent = Intent(context, PhoneNumberInputActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_NUMBER, currentNumber)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val currentNumber = intent.getStringExtra(EXTRA_CURRENT_NUMBER) ?: ""

        setContent {
            QuickCommandsTheme {
                PhoneNumberInputScreen(
                    currentNumber = currentNumber,
                    onNumberChanged = { number ->
                        // 返回输入结果
                        android.util.Log.d("PhoneNumberInput", "onNumberChanged调用，号码：$number")
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_PHONE_NUMBER, number)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onNavigateBack = {
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 电话号码输入界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhoneNumberInputScreen(
    currentNumber: String,
    onNumberChanged: (String) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    var phoneNumber by remember { mutableStateOf(currentNumber) }

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "电话号码",
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 保存按钮 - 主题感知
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的保存按钮（勾号图标）
                        SkyBlueSaveButton(
                            onClick = {
                                android.util.Log.d("PhoneNumberInput", "保存按钮点击，号码：$phoneNumber")
                                onNumberChanged(phoneNumber.trim())
                            },
                            enabled = phoneNumber.isNotBlank()
                        )
                    } else {
                        // 其他主题：使用原有的文本按钮
                        TextButton(
                            onClick = {
                                android.util.Log.d("PhoneNumberInput", "保存按钮点击，号码：$phoneNumber")
                                onNumberChanged(phoneNumber.trim())
                            },
                            enabled = phoneNumber.isNotBlank()
                        ) {
                            Text("保存")
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 使用新的卡片输入组件
                IOSStyleCardInput(
                    value = phoneNumber,
                    onValueChange = { phoneNumber = it },
                    placeholder = "例如：13800138000",
                    keyboardType = androidx.compose.ui.text.input.KeyboardType.Phone,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}
