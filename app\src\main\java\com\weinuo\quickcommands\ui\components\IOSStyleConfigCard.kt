package com.weinuo.quickcommands.ui.components

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Check
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import com.weinuo.quickcommands.R

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.theme.config.DividerConfig
import com.weinuo.quickcommands.ui.components.themed.ThemedCard

/**
 * iOS快捷指令风格的配置卡片组件
 *
 * 特点：
 * - 卡片风格与iOS快捷指令应用一致
 * - 主题感知的分割线显示（天空蓝主题显示，海洋蓝主题隐藏）
 * - 统一的圆角和间距设计
 * - 支持点击交互
 *
 * @param title 卡片标题
 * @param description 卡片描述（可选）
 * @param onClick 点击回调
 * @param modifier 修饰符
 * @param showDivider 是否显示分割线（会根据主题自动调整）
 */
@Composable
fun IOSStyleConfigCard(
    title: String,
    description: String? = null,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 主题感知的分割线显示逻辑
    val shouldShowDivider = remember(showDivider, themeManager.getCurrentThemeId()) {
        when (themeManager.getCurrentThemeId()) {
            "sky_blue" -> showDivider // 天空蓝主题：根据参数决定
            "ocean_blue" -> false     // 海洋蓝主题：隐藏分割线
            else -> showDivider       // 其他主题：根据参数决定
        }
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null // 移除涟漪效果，保持iOS风格
                ) { onClick() },
            shape = RoundedCornerShape(cardStyle.defaultCornerRadius),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerLow
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = cardStyle.defaultElevation
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = cardStyle.defaultHorizontalPadding,
                        vertical = cardStyle.settingsVerticalPadding
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧内容
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 标题
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }

                // 右侧内容：描述值和箭头
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 描述作为当前值显示在右侧
                    if (description != null) {
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // 向右箭头
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "进入",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }

        // 分割线（主题感知）
        if (shouldShowDivider) {
            Spacer(modifier = Modifier.height(1.dp))

            // 使用主题的分割线组件
            themeContext.componentFactory.createDivider()(
                DividerConfig(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
                )
            )
        }
    }
}



/**
 * iOS风格配置卡片数据类
 */
data class IOSStyleConfigCardData(
    val title: String,
    val description: String? = null,
    val onClick: () -> Unit
)

/**
 * 增强的配置项数据类
 *
 * 支持条件显示、描述值、选择状态等复杂逻辑
 */
data class EnhancedConfigItem(
    val title: String,
    val description: String? = null,
    val isVisible: Boolean = true,
    val isSelected: Boolean = false,
    val showArrow: Boolean = true,
    val showCheckIcon: Boolean = false,
    val onClick: () -> Unit
)

/**
 * 统一的选择列表卡片组件
 *
 * 用于联系人范围、筛选模式、收到短信配置等界面的选择列表
 * 与全局设置界面的卡片样式完全一致
 *
 * @param options 选项列表
 * @param currentSelection 当前选中的选项
 * @param onSelectionChanged 选择改变回调
 * @param modifier 修饰符
 * @param showCheckIcon 是否显示选中图标（默认true）
 */
@Composable
fun UnifiedSelectionListCard(
    options: List<String>,
    currentSelection: String,
    onSelectionChanged: (String) -> Unit,
    modifier: Modifier = Modifier,
    showCheckIcon: Boolean = true
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取UI间距配置
    val (settingsItemVerticalPadding, dividerHorizontalPadding, dividerVisible) = getUnifiedUISpacingConfig(context)

    // 使用ThemedCard替代单独的Card，保持与全局设置界面一致的样式
    ThemedCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            options.forEachIndexed { index, option ->
                // 选项内容行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果，保持iOS风格
                        ) { onSelectionChanged(option) }
                        .padding(
                            vertical = settingsItemVerticalPadding.dp,
                            horizontal = cardStyle.defaultHorizontalPadding
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧标题
                    Text(
                        text = option,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(1f)
                    )

                    // 右侧选中状态指示
                    if (showCheckIcon) {
                        if (themeManager.getCurrentThemeId() == "sky_blue") {
                            // 天空蓝主题：使用RadioButton样式的图标
                            val iconRes = if (option == currentSelection) {
                                R.drawable.ic_radio_button_checked_sky_blue
                            } else {
                                R.drawable.ic_radio_button_unchecked_sky_blue
                            }
                            Icon(
                                imageVector = ImageVector.vectorResource(iconRes),
                                contentDescription = if (option == currentSelection) "已选择" else "未选择",
                                tint = Color.Unspecified, // 使用SVG原始颜色
                                modifier = Modifier.size(20.dp)
                            )
                        } else {
                            // 其他主题：只在选中时显示勾选图标
                            if (option == currentSelection) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "已选择",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                        }
                    }
                }

                // 分割线（除了最后一项）
                if (index < options.size - 1) {
                    // 主题感知的分割线显示逻辑
                    val shouldShowDivider = remember(themeManager.getCurrentThemeId()) {
                        when (themeManager.getCurrentThemeId()) {
                            "sky_blue" -> dividerVisible  // 天空蓝主题：根据设置决定
                            "ocean_blue" -> false // 海洋蓝主题：隐藏分割线
                            else -> true        // 其他主题：显示分割线
                        }
                    }

                    if (shouldShowDivider) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = dividerHorizontalPadding.dp),
                                color = if (dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 增强的配置列表卡片组件
 *
 * 支持条件显示、描述值、选择状态等复杂逻辑
 * 用于收到短信配置等需要复杂配置项的界面
 *
 * @param configItems 配置项列表
 * @param modifier 修饰符
 */
@Composable
fun EnhancedConfigListCard(
    configItems: List<EnhancedConfigItem>,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取UI间距配置
    val (settingsItemVerticalPadding, dividerHorizontalPadding, dividerVisible) = getUnifiedUISpacingConfig(context)

    // 过滤可见的配置项
    val visibleItems = remember(configItems) {
        configItems.filter { it.isVisible }
    }

    if (visibleItems.isNotEmpty()) {
        // 使用ThemedCard替代单独的Card，保持与全局设置界面一致的样式
        ThemedCard(
            modifier = modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(0.dp)
            ) {
                visibleItems.forEachIndexed { index, item ->
                    // 配置项内容行
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null // 移除涟漪效果，保持iOS风格
                            ) { item.onClick() }
                            .padding(vertical = settingsItemVerticalPadding.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 左侧标题
                        Text(
                            text = item.title,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.weight(1f)
                        )

                        // 右侧内容：描述值和图标
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 描述作为当前值显示在右侧
                            if (item.description != null) {
                                Text(
                                    text = item.description,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.offset(x = 6.dp) // 向右偏移
                                )
                            }

                            // 图标显示
                            when {
                                item.showCheckIcon && item.isSelected -> {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选择",
                                        tint = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.size(24.dp)
                                    )
                                }
                                item.showArrow -> {
                                    Icon(
                                        imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                                        contentDescription = "进入",
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                                        modifier = Modifier
                                            .size(20.dp)
                                            .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                                    )
                                }
                            }
                        }
                    }

                    // 分割线（除了最后一项）
                    if (index < visibleItems.size - 1) {
                        // 主题感知的分割线显示逻辑
                        val shouldShowDivider = remember(themeManager.getCurrentThemeId()) {
                            when (themeManager.getCurrentThemeId()) {
                                "sky_blue" -> dividerVisible  // 天空蓝主题：根据设置决定
                                "ocean_blue" -> false // 海洋蓝主题：隐藏分割线
                                else -> true        // 其他主题：显示分割线
                            }
                        }

                        if (shouldShowDivider) {
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = dividerHorizontalPadding.dp),
                                    color = if (dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 获取统一的UI间距配置
 *
 * 用于所有需要与全局设置界面保持一致样式的界面
 *
 * @param context 上下文
 * @return Triple(settingsItemVerticalPadding, dividerHorizontalPadding, dividerVisible)
 */
@Composable
fun getUnifiedUISpacingConfig(context: Context): Triple<Int, Int, Boolean> {
    val themeManager = remember { ThemeManager.getInstance(context) }

    return if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        val globalSettings by settingsRepository.globalSettings.collectAsState()
        Triple(
            globalSettings.uiSettingsItemVerticalPadding,
            globalSettings.uiDividerHorizontalPadding,
            globalSettings.uiDividerVisible
        )
    } else {
        // 海洋蓝主题使用硬编码的默认设置值
        Triple(12, 0, true)
    }
}

/**
 * iOS风格的卡片输入框组件
 *
 * 特点：
 * - 卡片风格与iOS快捷指令应用一致
 * - 与IOSStyleConfigCard相同的样式，但右侧是输入框而不是文字
 * - 主题感知的样式配置
 * - 支持数字输入键盘
 *
 * @param value 当前输入值
 * @param onValueChange 值变更回调
 * @param placeholder 占位符文本
 * @param modifier 修饰符
 * @param keyboardType 键盘类型，默认为数字键盘
 * @param singleLine 是否单行输入，默认为true
 * @param enabled 是否启用输入
 */
@Composable
fun IOSStyleCardInput(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String = "",
    modifier: Modifier = Modifier,
    keyboardType: KeyboardType = KeyboardType.Number,
    singleLine: Boolean = true,
    enabled: Boolean = true
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current
    val focusRequester = remember { FocusRequester() }
    var isFocused by remember { mutableStateOf(false) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取UI间距配置，与EnhancedConfigListCard保持一致
    val (settingsItemVerticalPadding, _, _) = getUnifiedUISpacingConfig(context)

    // 使用ThemedCard替代单独的Card，保持与全局设置界面一致的样式
    ThemedCard(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 移除涟漪效果，保持iOS风格
            ) {
                if (enabled) {
                    focusRequester.requestFocus()
                }
            }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp, vertical = 4.dp), // 增加硬编码的水平padding
            contentAlignment = Alignment.CenterStart
        ) {
            BasicTextField(
                value = value,
                onValueChange = onValueChange,
                modifier = Modifier
                    .fillMaxWidth()
                    .focusRequester(focusRequester)
                    .onFocusChanged { focusState ->
                        isFocused = focusState.isFocused
                    },
                enabled = enabled,
                singleLine = singleLine,
                textStyle = TextStyle(
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                    fontWeight = FontWeight.Medium
                ),
                keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                decorationBox = { innerTextField ->
                    if (value.isEmpty() && !isFocused) {
                        Text(
                            text = placeholder,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    innerTextField()
                }
            )
        }
    }
}
